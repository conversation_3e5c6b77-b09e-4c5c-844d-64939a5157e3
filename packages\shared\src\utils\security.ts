import { z } from "zod";

/**
 * Security utilities for CSRF protection, rate limiting, and input validation
 */

// CSRF Token Management
export class CSRFManager {
  private static readonly TOKEN_LENGTH = 32;
  private static readonly TOKEN_HEADER = "x-csrf-token";
  // private static readonly COOKIE_NAME = "csrf-token"; // Reserved for future use

  /**
   * Generate a secure CSRF token
   */
  static generateToken(): string {
    // For Cloudflare Workers, use crypto.getRandomValues
    const array = new Uint8Array(CSRFManager.TOKEN_LENGTH);
    crypto.getRandomValues(array);
    return Array.from(array, (byte) => byte.toString(16).padStart(2, "0")).join("");
  }

  /**
   * Validate CSRF token from request
   */
  static validateToken(requestToken: string | null, sessionToken: string | null): boolean {
    if (!requestToken || !sessionToken) {
      return false;
    }

    // Constant-time comparison to prevent timing attacks
    return CSRFManager.constantTimeEqual(requestToken, sessionToken);
  }

  /**
   * Constant-time string comparison
   */
  private static constantTimeEqual(a: string, b: string): boolean {
    if (a.length !== b.length) {
      return false;
    }

    let result = 0;
    for (let i = 0; i < a.length; i++) {
      result |= a.charCodeAt(i) ^ b.charCodeAt(i);
    }

    return result === 0;
  }

  /**
   * Extract CSRF token from request headers or form data
   */
  static extractToken(request: Request): string | null {
    // Check header first
    const headerToken = request.headers.get(CSRFManager.TOKEN_HEADER);
    if (headerToken) return headerToken;

    // Check form data for POST requests
    if (request.method === "POST") {
      const contentType = request.headers.get("content-type");
      if (contentType?.includes("application/x-www-form-urlencoded")) {
        // This would need to be handled in the caller since we can't read body twice
        return null;
      }
    }

    return null;
  }
}

// Rate Limiting
export interface RateLimitConfig {
  windowMs: number; // Time window in milliseconds
  maxRequests: number; // Maximum requests per window
  keyGenerator?: (request: Request) => string; // Custom key generator
}

export class RateLimiter {
  private requests = new Map<string, { count: number; resetTime: number }>();

  constructor(private config: RateLimitConfig) {}

  /**
   * Check if request is within rate limit
   */
  isAllowed(request: Request): { allowed: boolean; remaining: number; resetTime: number } {
    const key = this.config.keyGenerator
      ? this.config.keyGenerator(request)
      : this.getDefaultKey(request);

    const now = Date.now();
    const entry = this.requests.get(key);

    // Clean up expired entries
    this.cleanup(now);

    if (!entry || now > entry.resetTime) {
      // First request or window expired
      const resetTime = now + this.config.windowMs;
      this.requests.set(key, { count: 1, resetTime });
      return {
        allowed: true,
        remaining: this.config.maxRequests - 1,
        resetTime,
      };
    }

    if (entry.count >= this.config.maxRequests) {
      return {
        allowed: false,
        remaining: 0,
        resetTime: entry.resetTime,
      };
    }

    entry.count++;
    return {
      allowed: true,
      remaining: this.config.maxRequests - entry.count,
      resetTime: entry.resetTime,
    };
  }

  private getDefaultKey(request: Request): string {
    // Extract IP from CF-Connecting-IP header (Cloudflare) or fallback
    const ip =
      request.headers.get("cf-connecting-ip") ||
      request.headers.get("x-forwarded-for") ||
      request.headers.get("x-real-ip") ||
      "unknown";

    return `ip:${ip}`;
  }

  private cleanup(now: number): void {
    for (const [key, entry] of this.requests.entries()) {
      if (now > entry.resetTime) {
        this.requests.delete(key);
      }
    }
  }
}

// Input Sanitization
export class InputSanitizer {
  /**
   * Sanitize HTML input to prevent XSS
   */
  static sanitizeHtml(input: string): string {
    return input
      .replace(/&/g, "&amp;")
      .replace(/</g, "&lt;")
      .replace(/>/g, "&gt;")
      .replace(/"/g, "&quot;")
      .replace(/'/g, "&#x27;")
      .replace(/\//g, "&#x2F;");
  }

  /**
   * Sanitize SQL input (basic protection - use parameterized queries instead)
   */
  static sanitizeSql(input: string): string {
    return input.replace(/['"\\;]/g, "");
  }

  /**
   * Validate and sanitize file names
   */
  static sanitizeFileName(fileName: string): string {
    return fileName
      .replace(/[^a-zA-Z0-9.-]/g, "_")
      .replace(/\.{2,}/g, ".")
      .replace(/^\.+|\.+$/g, "")
      .substring(0, 255);
  }

  /**
   * Remove null bytes and control characters
   */
  static removeControlChars(input: string): string {
    return input.replace(/[\x00-\x1F\x7F]/g, "");
  }
}

// Security Headers
export class SecurityHeaders {
  /**
   * Get recommended security headers for Cloudflare Workers
   */
  static getSecurityHeaders(): Headers {
    const headers = new Headers();

    // Prevent clickjacking
    headers.set("X-Frame-Options", "DENY");

    // Prevent MIME type sniffing
    headers.set("X-Content-Type-Options", "nosniff");

    // Enable XSS protection
    headers.set("X-XSS-Protection", "1; mode=block");

    // Strict Transport Security (HTTPS only)
    headers.set("Strict-Transport-Security", "max-age=31536000; includeSubDomains");

    // Content Security Policy
    headers.set(
      "Content-Security-Policy",
      [
        "default-src 'self'",
        "script-src 'self' 'unsafe-inline' https://js.stripe.com",
        "style-src 'self' 'unsafe-inline'",
        "img-src 'self' data: https:",
        "font-src 'self'",
        "connect-src 'self' https://api.stripe.com",
        "frame-src https://js.stripe.com",
      ].join("; ")
    );

    // Referrer Policy
    headers.set("Referrer-Policy", "strict-origin-when-cross-origin");

    // Permissions Policy
    headers.set(
      "Permissions-Policy",
      ["camera=()", "microphone=()", "geolocation=()", "interest-cohort=()"].join(", ")
    );

    return headers;
  }

  /**
   * Apply security headers to a response
   */
  static applyToResponse(response: Response): Response {
    const securityHeaders = SecurityHeaders.getSecurityHeaders();
    const newHeaders = new Headers(response.headers);

    for (const [key, value] of securityHeaders.entries()) {
      newHeaders.set(key, value);
    }

    return new Response(response.body, {
      status: response.status,
      statusText: response.statusText,
      headers: newHeaders,
    });
  }
}

// Request Validation Schemas
export const requestValidationSchemas = {
  // Common patterns
  uuid: z.string().uuid("Invalid UUID format"),
  slug: z.string().regex(/^[a-z0-9-]+$/, "Invalid slug format"),
  email: z.string().email("Invalid email format"),
  url: z.string().url("Invalid URL format"),

  // Security-focused validation
  password: z
    .string()
    .min(8, "Password must be at least 8 characters")
    .regex(/[A-Z]/, "Password must contain uppercase letter")
    .regex(/[a-z]/, "Password must contain lowercase letter")
    .regex(/[0-9]/, "Password must contain number")
    .regex(/[^A-Za-z0-9]/, "Password must contain special character"),

  // File upload validation
  fileName: z
    .string()
    .min(1, "File name is required")
    .max(255, "File name too long")
    .regex(/^[^<>:"/\\|?*\x00-\x1f]+$/, "Invalid file name"),

  // API key validation
  apiKey: z
    .string()
    .min(20, "API key too short")
    .regex(/^[A-Za-z0-9_-]+$/, "Invalid API key format"),
};

/**
 * Middleware factory for CSRF protection
 */
export function createCSRFMiddleware() {
  return async (request: Request, next: () => Promise<Response>): Promise<Response> => {
    // Skip CSRF for GET, HEAD, OPTIONS
    if (["GET", "HEAD", "OPTIONS"].includes(request.method)) {
      return next();
    }

    const token = CSRFManager.extractToken(request);
    const sessionToken = request.headers
      .get("cookie")
      ?.split(";")
      .find((c) => c.trim().startsWith("csrf-token="))
      ?.split("=")[1];

    if (!CSRFManager.validateToken(token, sessionToken || null)) {
      return new Response("CSRF token validation failed", { status: 403 });
    }

    return next();
  };
}

/**
 * Middleware factory for rate limiting
 */
export function createRateLimitMiddleware(config: RateLimitConfig) {
  const limiter = new RateLimiter(config);

  return async (request: Request, next: () => Promise<Response>): Promise<Response> => {
    const result = limiter.isAllowed(request);

    if (!result.allowed) {
      const response = new Response("Rate limit exceeded", { status: 429 });
      response.headers.set(
        "Retry-After",
        Math.ceil((result.resetTime - Date.now()) / 1000).toString()
      );
      return response;
    }

    const response = await next();
    response.headers.set("X-RateLimit-Remaining", result.remaining.toString());
    response.headers.set("X-RateLimit-Reset", Math.ceil(result.resetTime / 1000).toString());

    return response;
  };
}

/**
 * Security middleware composer
 */
export function createSecurityMiddleware(
  options: { csrf?: boolean; rateLimit?: RateLimitConfig; headers?: boolean } = {}
) {
  const middlewares: Array<(request: Request, next: () => Promise<Response>) => Promise<Response>> =
    [];

  if (options.csrf) {
    middlewares.push(createCSRFMiddleware());
  }

  if (options.rateLimit) {
    middlewares.push(createRateLimitMiddleware(options.rateLimit));
  }

  return async (request: Request, handler: () => Promise<Response>): Promise<Response> => {
    let next = handler;

    // Apply middlewares in reverse order
    for (let i = middlewares.length - 1; i >= 0; i--) {
      const middleware = middlewares[i];
      if (middleware) {
        const currentNext = next;
        next = () => middleware(request, currentNext);
      }
    }

    const response = await next();

    // Apply security headers if requested
    if (options.headers) {
      return SecurityHeaders.applyToResponse(response);
    }

    return response;
  };
}
