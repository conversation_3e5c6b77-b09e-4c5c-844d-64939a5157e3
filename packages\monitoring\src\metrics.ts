/**
 * Metrics collection and performance monitoring for Cloudflare Workers
 */

export interface Metric {
  name: string;
  value: number;
  timestamp: string;
  tags?: Record<string, string>;
  type: "counter" | "gauge" | "histogram" | "timer";
}

export interface TimerResult {
  duration: number;
  end: () => void;
}

export class MetricsCollector {
  private metrics: Metric[] = [];
  private timers = new Map<string, number>();

  /**
   * Increment a counter metric
   */
  increment(name: string, value = 1, tags?: Record<string, string>): void {
    this.addMetric({
      name,
      value,
      timestamp: new Date().toISOString(),
      tags,
      type: "counter",
    });
  }

  /**
   * Set a gauge metric (current value)
   */
  gauge(name: string, value: number, tags?: Record<string, string>): void {
    this.addMetric({
      name,
      value,
      timestamp: new Date().toISOString(),
      tags,
      type: "gauge",
    });
  }

  /**
   * Record a histogram value
   */
  histogram(name: string, value: number, tags?: Record<string, string>): void {
    this.addMetric({
      name,
      value,
      timestamp: new Date().toISOString(),
      tags,
      type: "histogram",
    });
  }

  /**
   * Start a timer
   */
  startTimer(name: string): TimerResult {
    const start = Date.now();
    this.timers.set(name, start);

    return {
      get duration() {
        return Date.now() - start;
      },
      end: () => {
        const duration = Date.now() - start;
        this.timers.delete(name);
        this.addMetric({
          name,
          value: duration,
          timestamp: new Date().toISOString(),
          type: "timer",
        });
        return duration;
      },
    };
  }

  /**
   * Time a function execution
   */
  async time<T>(
    name: string,
    fn: () => Promise<T>,
    tags?: Record<string, string>
  ): Promise<T> {
    const timer = this.startTimer(name);
    try {
      const result = await fn();
      timer.end();
      this.addMetric({
        name: `${name}.success`,
        value: 1,
        timestamp: new Date().toISOString(),
        tags,
        type: "counter",
      });
      return result;
    } catch (error) {
      timer.end();
      this.addMetric({
        name: `${name}.error`,
        value: 1,
        timestamp: new Date().toISOString(),
        tags: { ...tags, error: (error as Error).name },
        type: "counter",
      });
      throw error;
    }
  }

  private addMetric(metric: Metric): void {
    this.metrics.push(metric);

    // Prevent memory leaks by limiting stored metrics
    if (this.metrics.length > 1000) {
      this.metrics = this.metrics.slice(-500);
    }
  }

  /**
   * Get all collected metrics
   */
  getMetrics(): Metric[] {
    return [...this.metrics];
  }

  /**
   * Clear all metrics
   */
  clearMetrics(): void {
    this.metrics = [];
  }

  /**
   * Get metrics in Prometheus format
   */
  getPrometheusMetrics(): string {
    const lines: string[] = [];
    const metricGroups = new Map<string, Metric[]>();

    // Group metrics by name
    for (const metric of this.metrics) {
      if (!metricGroups.has(metric.name)) {
        metricGroups.set(metric.name, []);
      }
      metricGroups.get(metric.name)!.push(metric);
    }

    // Convert to Prometheus format
    for (const [name, metrics] of metricGroups) {
      const metric = metrics[metrics.length - 1]; // Get latest metric
      if (!metric) continue; // Skip if no metrics available

      const metricName = name.replace(/[^a-zA-Z0-9_]/g, "_");

      if (metric.tags) {
        const tags = Object.entries(metric.tags)
          .map(([key, value]) => `${key}="${value}"`)
          .join(",");
        lines.push(`${metricName}{${tags}} ${metric.value}`);
      } else {
        lines.push(`${metricName} ${metric.value}`);
      }
    }

    return lines.join("\n");
  }
}

/**
 * Business metrics for SaaS applications
 */
export class BusinessMetrics {
  constructor(private collector: MetricsCollector) {}

  /**
   * Track user registration
   */
  userRegistered(accountType: "personal" | "team" = "personal"): void {
    this.collector.increment("users.registered", 1, { account_type: accountType });
  }

  /**
   * Track user login
   */
  userLogin(method: "password" | "oauth" = "password"): void {
    this.collector.increment("users.login", 1, { method });
  }

  /**
   * Track subscription events
   */
  subscriptionCreated(plan: string, amount: number): void {
    this.collector.increment("subscriptions.created", 1, { plan });
    this.collector.gauge("revenue.mrr", amount, { plan });
  }

  subscriptionCanceled(plan: string, reason?: string): void {
    this.collector.increment("subscriptions.canceled", 1, { 
      plan, 
      reason: reason || "unknown" 
    });
  }

  /**
   * Track payment events
   */
  paymentSucceeded(amount: number, currency = "usd"): void {
    this.collector.increment("payments.succeeded", 1, { currency });
    this.collector.histogram("payments.amount", amount, { currency });
  }

  paymentFailed(reason: string, currency = "usd"): void {
    this.collector.increment("payments.failed", 1, { reason, currency });
  }

  /**
   * Track API usage
   */
  apiRequest(endpoint: string, method: string): void {
    this.collector.increment("api.requests", 1, { endpoint, method });
  }

  apiError(endpoint: string, status: number): void {
    this.collector.increment("api.errors", 1, { 
      endpoint, 
      status: status.toString() 
    });
  }

  /**
   * Track feature usage
   */
  featureUsed(feature: string, userId?: string): void {
    const tags: Record<string, string> = { feature };
    if (userId) tags.user_id = userId;
    this.collector.increment("features.used", 1, tags);
  }

  /**
   * Track database performance
   */
  databaseQuery(table: string, operation: "select" | "insert" | "update" | "delete", duration: number): void {
    this.collector.histogram("database.query.duration", duration, { 
      table, 
      operation 
    });
    this.collector.increment("database.queries", 1, { table, operation });
  }

  /**
   * Track external service calls
   */
  externalServiceCall(service: string, operation: string, duration: number, success: boolean): void {
    this.collector.histogram("external.service.duration", duration, { 
      service, 
      operation 
    });
    this.collector.increment("external.service.calls", 1, { 
      service, 
      operation, 
      status: success ? "success" : "error" 
    });
  }
}

/**
 * Performance monitoring utilities
 */
export class PerformanceMonitor {
  constructor(private collector: MetricsCollector) {}

  /**
   * Monitor memory usage (for Workers with access to performance API)
   */
  recordMemoryUsage(): void {
    if (typeof performance !== "undefined" && "memory" in performance) {
      const memory = (performance as any).memory;
      this.collector.gauge("memory.used", memory.usedJSHeapSize);
      this.collector.gauge("memory.total", memory.totalJSHeapSize);
      this.collector.gauge("memory.limit", memory.jsHeapSizeLimit);
    }
  }

  /**
   * Monitor request size
   */
  recordRequestSize(size: number): void {
    this.collector.histogram("request.size", size);
  }

  /**
   * Monitor response size
   */
  recordResponseSize(size: number): void {
    this.collector.histogram("response.size", size);
  }

  /**
   * Monitor cache hit/miss
   */
  recordCacheHit(cache: string): void {
    this.collector.increment("cache.hits", 1, { cache });
  }

  recordCacheMiss(cache: string): void {
    this.collector.increment("cache.misses", 1, { cache });
  }

  /**
   * Monitor worker CPU time (if available)
   */
  recordCPUTime(time: number): void {
    this.collector.histogram("cpu.time", time);
  }
}

/**
 * Global metrics instance
 */
let globalCollector: MetricsCollector | undefined;
let globalBusinessMetrics: BusinessMetrics | undefined;
let globalPerformanceMonitor: PerformanceMonitor | undefined;

export function getMetricsCollector(): MetricsCollector {
  if (!globalCollector) {
    globalCollector = new MetricsCollector();
  }
  return globalCollector;
}

export function getBusinessMetrics(): BusinessMetrics {
  if (!globalBusinessMetrics) {
    globalBusinessMetrics = new BusinessMetrics(getMetricsCollector());
  }
  return globalBusinessMetrics;
}

export function getPerformanceMonitor(): PerformanceMonitor {
  if (!globalPerformanceMonitor) {
    globalPerformanceMonitor = new PerformanceMonitor(getMetricsCollector());
  }
  return globalPerformanceMonitor;
}

/**
 * Metrics middleware for automatic request/response monitoring
 */
export function createMetricsMiddleware(collector?: MetricsCollector) {
  const metricsCollector = collector || getMetricsCollector();
  const performanceMonitor = new PerformanceMonitor(metricsCollector);

  return async (
    request: Request,
    handler: () => Promise<Response>
  ): Promise<Response> => {
    const timer = metricsCollector.startTimer("request.duration");
    const url = new URL(request.url);
    const endpoint = url.pathname;

    // Record request metrics
    metricsCollector.increment("requests.total", 1, {
      method: request.method,
      endpoint,
    });

    // Record request size if available
    const requestSize = request.headers.get("content-length");
    if (requestSize) {
      performanceMonitor.recordRequestSize(parseInt(requestSize, 10));
    }

    try {
      const response = await handler();
      const duration = timer.end();

      // Record success metrics
      metricsCollector.increment("requests.success", 1, {
        method: request.method,
        endpoint,
        status: response.status.toString(),
      });

      // Record response size if available
      const responseSize = response.headers.get("content-length");
      if (responseSize) {
        performanceMonitor.recordResponseSize(parseInt(responseSize, 10));
      }

      return response;
    } catch (error) {
      timer.end();

      // Record error metrics
      metricsCollector.increment("requests.error", 1, {
        method: request.method,
        endpoint,
        error: (error as Error).name,
      });

      throw error;
    }
  };
}