# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a production-ready SaaS starter template built with Remix, Cloudflare Workers, and Neon PostgreSQL. The project uses a clean 2-layer monorepo architecture designed for scalability and modern development practices.

## Development Commands

### Build System
- `pnpm build` - Build all apps and packages
- `pnpm dev` - Start development servers for all apps
- `pnpm lint` - Run Biome linting across all packages
- `pnpm format` - Format code with Biome
- `pnpm check-types` - Run TypeScript type checking

### Database Commands
- `pnpm db:push` - Push database schema changes to development
- `pnpm db:generate` - Generate database migrations
- `pnpm db:migrate` - Run database migrations
- `pnpm db:studio` - Open Drizzle Studio for database management

### Filtered Commands (Turbo)
- `pnpm turbo dev --filter=web` - Start only the web app in development
- `pnpm turbo dev --filter=worker` - Start only the worker app in development
- `pnpm turbo build --filter=web` - Build only the web app
- `pnpm turbo build --filter=worker` - Build only the worker app
- `pnpm turbo build --filter=ui-kit` - Build only the UI kit package

### Individual App Commands
- **Web app**: `cd apps/web && pnpm dev` (runs on port 5173, accessible at http://localhost:5133)
- **Worker app**: `cd apps/worker && pnpm dev` (runs on port 8787)

## Architecture

### Monorepo Structure
```
├── apps/
│   ├── web/        # Main Remix SaaS application
│   ├── worker/     # Background Cloudflare Worker
│   └── e2e/        # End-to-end testing (planned)
└── packages/
    ├── auth/       # BetterAuth authentication system
    ├── db/         # Database schemas and migrations (Drizzle ORM)
    ├── config/     # Environment configuration management
    ├── ui-kit/     # React component library with Radix UI
    ├── shared/     # Common utilities, types, and constants
    ├── billing/    # Stripe payment integration
    ├── storage/    # Cloudflare R2 and S3 file storage
    ├── mailer/     # Email functionality
    ├── monitoring/ # Logging and error tracking
    ├── ai/         # AI integration (placeholder)
    ├── analytics/  # User analytics (placeholder)
    ├── cms/        # Content management (placeholder)
    └── [others]/   # Additional planned packages
```

### Key Technologies
- **Runtime**: Cloudflare Workers
- **Frontend**: Remix 2.16.8 with React 19.1.0
- **Database**: Neon PostgreSQL with Drizzle ORM
- **Authentication**: BetterAuth
- **Styling**: Tailwind CSS with CSS variables
- **Components**: Radix UI primitives
- **Build**: Turborepo with pnpm workspaces
- **Code Quality**: Biome (replaces ESLint + Prettier)
- **TypeScript**: 5.8.2

### Package Exports
- `@repo/shared` exports: `./types`, `./constants`, `./utils`
- `@repo/ui-kit` exports: `.`, `./components/*`, `./primitives/*`, `./styles/*`, `./utils`
- `@repo/auth` exports: `.`, `./config`, `./providers`
- `@repo/db` exports: `.`, `./schema`, `./migrations`
- `@repo/config` exports: `.`, `./env`

## Development Workflow

### Adding New Components to UI Kit
Use the component generator: `cd packages/ui-kit && pnpm generate:component`

### Creating New Packages
1. Create directory in `packages/` folder
2. Add package.json with `@repo/` prefix
3. Add to `pnpm-workspace.yaml`
4. Configure exports in package.json
5. Add build configuration to turbo.json

### Package Dependencies
- Use `workspace:*` for internal package dependencies
- All packages use TypeScript 5.8.2 and React 19.1.0
- Biome configured for consistent code formatting and linting
- Strict TypeScript configuration with `noEmit: true`

### Build Pipeline
- Turbo handles build orchestration with dependency awareness
- TypeScript compilation runs in parallel across packages
- Outputs cached for performance (`dist/` directories)
- Supports incremental builds for faster development

### Database Workflow
1. Make schema changes in `packages/db/src/schema.ts`
2. Run `pnpm db:push` for development changes
3. Run `pnpm db:generate` to create migration files
4. Run `pnpm db:migrate` for production deployments

## Important Notes

### Package Manager
This project uses `pnpm` as the package manager. Always use `pnpm` commands, not `npm` or `yarn`.

### Monorepo Conventions
- All internal packages are prefixed with `@repo/`
- Shared TypeScript configurations in `packages/typescript-config`
- Use Turbo filters for targeted builds and development
- Workspace dependencies should use `workspace:*` protocol

### Code Quality
- Biome handles both linting and formatting (no ESLint/Prettier)
- Strict TypeScript configuration with comprehensive type checking
- All packages must build without warnings
- Use TypeScript 5.8.2 features and React 19 patterns

### Deployment Target
- Primary deployment target is Cloudflare Workers
- Web app uses Remix with Cloudflare Workers adapter
- Worker app is standalone Cloudflare Worker
- Database hosted on Neon PostgreSQL

### Authentication
- BetterAuth handles authentication with multiple providers
- Session management integrated with database
- Role-based access control ready for implementation

### Environment Configuration
- Environment variables managed through `@repo/config`
- Type-safe environment variable loading
- Separate configurations for development and production