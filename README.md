# Remix Cloudflare Neon Starter

A modern, production-ready SaaS starter template built with Remix, Cloudflare Workers, and Neon PostgreSQL. This comprehensive monorepo provides everything you need to build and deploy a scalable SaaS application.

## ✨ What's Included

- 🚀 **Remix** application with Cloudflare Workers deployment
- 🔐 **BetterAuth** authentication system with multiple providers
- 🗄️ **Neon PostgreSQL** database with Drizzle ORM
- 🎨 **Radix UI** component library with Tailwind CSS
- 📧 **Email** functionality with mailer integration
- 💳 **Stripe** billing integration (in development)
- 📊 **Analytics** and monitoring setup
- 🤖 **AI** integration capabilities
- 📱 **Responsive** design with dark/light theme support

## 🏗️ Architecture

This starter uses a clean 2-layer monorepo architecture:

```
├── apps/                    # Applications
│   ├── web/                # Main Remix SaaS application (port 5133)
│   ├── worker/             # Background Cloudflare Worker (port 8787)
│   └── e2e/                # End-to-end testing (planned)
└── packages/               # Shared packages
    ├── auth/               # BetterAuth authentication system
    ├── db/                 # Database schemas and migrations
    ├── ui-kit/             # React component library
    ├── shared/             # Common utilities and types
    ├── config/             # Environment configuration
    ├── billing/            # Stripe payment integration
    ├── storage/            # Cloudflare R2 file storage
    ├── mailer/             # Email functionality
    ├── monitoring/         # Logging and error tracking
    └── [others]/           # Additional feature packages
```

## 🚀 Tech Stack

### Core Technologies
- **Frontend**: [Remix](https://remix.run/) + [React 19](https://reactjs.org/)
- **Runtime**: [Cloudflare Workers](https://workers.cloudflare.com/)
- **Database**: [Neon PostgreSQL](https://neon.tech/) + [Drizzle ORM](https://orm.drizzle.team/)
- **Authentication**: [BetterAuth](https://better-auth.com/)
- **Storage**: [Cloudflare R2](https://developers.cloudflare.com/r2/)
- **Payments**: [Stripe](https://stripe.com/)

### Development Experience
- **Language**: [TypeScript 5.8](https://www.typescriptlang.org/)
- **Build Tool**: [Turbo](https://turbo.build/)
- **Package Manager**: [pnpm](https://pnpm.io/)
- **Styling**: [Tailwind CSS](https://tailwindcss.com/)
- **Components**: [Radix UI](https://www.radix-ui.com/)
- **Code Quality**: [Biome](https://biomejs.dev/) (formatting & linting)

## 📦 Package Overview

### Core Packages (Active Development)

#### `@repo/auth` - Authentication System
- BetterAuth integration with multiple providers
- Session management and user context
- Role-based access control ready

#### `@repo/db` - Database Layer  
- Drizzle ORM with Neon PostgreSQL
- Type-safe database operations
- Migration system included

#### `@repo/ui-kit` - Component Library
- 20+ React components built with Radix UI
- Tailwind CSS styling with CSS variables
- Dark/light theme support
- TypeScript definitions included

#### `@repo/shared` - Common Utilities
- TypeScript types and interfaces
- Validation schemas with Zod
- Utility functions and constants

#### `@repo/config` - Configuration Management
- Environment variable management
- Type-safe configuration loading

### Feature Packages (Ready for Development)

#### `@repo/billing` - Payment Integration
- Stripe integration setup
- Subscription management (in progress)
- Webhook handling

#### `@repo/storage` - File Management
- Cloudflare R2 and S3 providers
- File upload utilities

#### `@repo/mailer` - Email System
- Email sending capabilities
- Template management ready

#### `@repo/monitoring` - Observability
- Error tracking and logging
- Performance monitoring setup

### Planned Packages (Roadmap)
- `@repo/ai` - AI/ML integration
- `@repo/analytics` - User analytics
- `@repo/cms` - Content management
- `@repo/i18n` - Internationalization
- `@repo/email-templates` - Email templates

## 🚀 Quick Start

### Prerequisites
- [Node.js](https://nodejs.org/) 18+ 
- [pnpm](https://pnpm.io/) (recommended package manager)
- [Cloudflare account](https://cloudflare.com/) for deployment
- [Neon PostgreSQL](https://neon.tech/) database

### Installation

1. **Clone and install dependencies:**
   ```bash
   git clone <your-repo>
   cd remix-cloudflare-neon-starter
   pnpm install
   ```

2. **Set up environment variables:**
   ```bash
   cp apps/web/.env.example apps/web/.env.local
   # Configure your database, auth, and Cloudflare settings
   ```

3. **Initialize the database:**
   ```bash
   pnpm db:push
   ```

4. **Start development:**
   ```bash
   pnpm dev
   ```

Visit http://localhost:5133 for the web app and http://localhost:8787 for the worker.

## 🛠️ Development Commands

### Global Commands
```bash
pnpm dev              # Start all applications
pnpm build            # Build all apps and packages  
pnpm lint             # Run Biome linting
pnpm format           # Format code with Biome
pnpm check-types      # TypeScript type checking
pnpm db:push          # Push database schema changes
pnpm db:studio        # Open Drizzle Studio
```

### Filtered Development
```bash
# Start only specific apps
pnpm turbo dev --filter=web     # Web app only
pnpm turbo dev --filter=worker  # Worker only

# Build specific packages
pnpm turbo build --filter=ui-kit
pnpm turbo build --filter=auth
```

### Component Development
```bash
# Generate new UI components
cd packages/ui-kit
pnpm generate:component
```

## 🏗️ Project Structure Details

### Apps
- **`apps/web/`** - Main Remix SaaS application
  - Authentication pages and dashboard
  - User management and billing UI
  - Responsive design with theme support

- **`apps/worker/`** - Background Cloudflare Worker
  - API endpoints and background jobs
  - Webhook handlers
  - Scheduled tasks

### Key Package Exports
- **`@repo/shared`**: `./types`, `./constants`, `./utils`
- **`@repo/ui-kit`**: `.`, `./components/*`, `./primitives/*`, `./styles/*`
- **`@repo/auth`**: `.`, `./config`, `./providers`
- **`@repo/db`**: `.`, `./schema`, `./migrations`

## 🚀 Deployment

### Cloudflare Workers Deployment

1. **Configure Cloudflare:**
   ```bash
   # Install Wrangler CLI
   npm install -g wrangler
   
   # Login to Cloudflare
   wrangler login
   ```

2. **Deploy the applications:**
   ```bash
   # Deploy web app
   cd apps/web
   pnpm deploy
   
   # Deploy worker
   cd apps/worker  
   pnpm deploy
   ```

3. **Set up environment variables in Cloudflare dashboard**

## 🔧 Customization

### Adding New Features
1. Create new package in `packages/`
2. Add to `pnpm-workspace.yaml`
3. Export from main package entry point
4. Import in your applications

### Database Changes
```bash
# Make schema changes in packages/db/src/schema.ts
pnpm db:push          # Push to development
pnpm db:generate      # Generate migration
pnpm db:migrate       # Run migrations
```

### UI Components
```bash
# Add components to packages/ui-kit/src/components/
pnpm generate:component  # Use the generator
```

## 📚 Documentation

- [Remix Documentation](https://remix.run/docs)
- [Cloudflare Workers](https://developers.cloudflare.com/workers/)
- [Drizzle ORM](https://orm.drizzle.team/)
- [BetterAuth](https://better-auth.com/)
- [Radix UI](https://www.radix-ui.com/)
- [Tailwind CSS](https://tailwindcss.com/)

## 🤝 Contributing

This is a starter template. Feel free to:
- Fork and customize for your needs
- Submit issues and feature requests
- Create pull requests for improvements

## 📄 License

MIT License - see LICENSE file for details.
