{"name": "@repo/monitoring", "version": "0.1.0", "private": true, "type": "module", "main": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": "./dist/index.js", "./logger": "./dist/logger.js", "./metrics": "./dist/metrics.js", "./errors": "./dist/errors.js"}, "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "type-check": "tsc --noEmit"}, "dependencies": {"@repo/shared": "workspace:*", "zod": "^3.22.4"}, "devDependencies": {"@repo/typescript-config": "workspace:*", "typescript": "^5.8.2"}}