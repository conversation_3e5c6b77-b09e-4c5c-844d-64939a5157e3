import type { ActionFunctionArgs, LoaderFunctionArgs } from "@remix-run/cloudflare";
import { json, redirect } from "@remix-run/cloudflare";
import { Form, useActionData, useNavigation } from "@remix-run/react";
import {
  <PERSON><PERSON>,
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  Input,
  Label,
} from "@repo/ui-kit";
import { getAuth, getUser } from "~/lib/auth.server";

export async function loader({ request, context }: LoaderFunctionArgs) {
  // Redirect to dashboard if already logged in
  const user = await getUser(request, context);
  if (user) {
    return redirect("/dashboard");
  }
  return json({});
}

export async function action({ request, context }: ActionFunctionArgs) {
  const auth = getAuth(context);
  const formData = await request.formData();
  const intent = formData.get("intent");

  try {
    if (intent === "email-signup") {
      const email = formData.get("email")?.toString();
      const password = formData.get("password")?.toString();
      const name = formData.get("name")?.toString();

      if (!email || !password || !name) {
        return json({ error: "Email, password, and name are required" }, { status: 400 });
      }

      if (password.length < 8) {
        return json({ error: "Password must be at least 8 characters long" }, { status: 400 });
      }

      const result = await auth.api.signUpEmail({
        body: { email, password, name },
        headers: request.headers,
      });

      if (result.error) {
        return json({ error: result.error.message }, { status: 400 });
      }

      // Set session cookie and redirect
      return redirect("/dashboard", {
        headers: {
          "Set-Cookie": result.data?.cookie || "",
        },
      });
    }

    if (intent === "google-signup") {
      const redirectURL = await auth.api.getOAuthRedirect({
        query: { provider: "google", callbackURL: "/auth/callback" },
        headers: request.headers,
      });

      return redirect(redirectURL.data?.url || "/");
    }

    return json({ error: "Invalid intent" }, { status: 400 });
  } catch (error) {
    console.error("Signup error:", error);
    return json({ error: "An unexpected error occurred" }, { status: 500 });
  }
}

export default function Signup() {
  const actionData = useActionData<typeof action>();
  const navigation = useNavigation();
  const isSubmitting = navigation.state === "submitting";

  return (
    <div className="flex min-h-screen items-center justify-center bg-gray-50 px-4 py-12 sm:px-6 lg:px-8">
      <div className="w-full max-w-md space-y-8">
        <Card>
          <CardHeader className="space-y-1">
            <CardTitle className="text-2xl text-center">Create your account</CardTitle>
            <CardDescription className="text-center">
              Enter your details below to create your account
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {actionData?.error && (
              <div className="text-sm text-red-600 bg-red-50 p-3 rounded-md">
                {actionData.error}
              </div>
            )}

            {/* Google OAuth Signup */}
            <Form method="post">
              <input type="hidden" name="intent" value="google-signup" />
              <Button type="submit" variant="outline" className="w-full" disabled={isSubmitting}>
                Continue with Google
              </Button>
            </Form>

            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <span className="w-full border-t" />
              </div>
              <div className="relative flex justify-center text-xs uppercase">
                <span className="bg-white px-2 text-gray-500">Or continue with</span>
              </div>
            </div>

            {/* Email/Password Signup */}
            <Form method="post" className="space-y-4">
              <input type="hidden" name="intent" value="email-signup" />

              <div className="space-y-2">
                <Label htmlFor="name">Full Name</Label>
                <Input
                  id="name"
                  name="name"
                  type="text"
                  autoComplete="name"
                  required
                  placeholder="Enter your full name"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  autoComplete="email"
                  required
                  placeholder="Enter your email"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="password">Password</Label>
                <Input
                  id="password"
                  name="password"
                  type="password"
                  autoComplete="new-password"
                  required
                  placeholder="Enter your password (min. 8 characters)"
                />
              </div>

              <Button type="submit" className="w-full" disabled={isSubmitting}>
                {isSubmitting ? "Creating account..." : "Create account"}
              </Button>
            </Form>

            <div className="text-center text-sm">
              Already have an account?{" "}
              <a href="/login" className="font-medium text-blue-600 hover:text-blue-500">
                Sign in
              </a>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
