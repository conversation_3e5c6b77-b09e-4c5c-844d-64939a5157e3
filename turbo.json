{"$schema": "https://turborepo.com/schema.json", "ui": "tui", "tasks": {"build": {"dependsOn": ["^build"], "inputs": ["$TURBO_DEFAULT$", ".env*"], "outputs": ["dist/**", "build/**"]}, "deploy": {"dependsOn": ["build"], "inputs": ["$TURBO_DEFAULT$", ".env*"], "cache": false}, "lint": {"dependsOn": ["^lint"]}, "lint:fix": {"dependsOn": ["^lint:fix"]}, "check-types": {"dependsOn": ["^check-types"]}, "dev": {"cache": false, "persistent": true}}}