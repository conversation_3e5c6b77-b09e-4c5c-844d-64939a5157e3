/**
 * <PERSON>flare Worker for API services
 * Handles R2 pre-signed URLs, webhooks, and background tasks
 */

export interface Env {
  ASSETS: R2Bucket;
  CACHE: KVNamespace;
  NODE_ENV: string;
}

export default {
  async fetch(request: Request, env: Env, _ctx: ExecutionContext): Promise<Response> {
    const url = new URL(request.url);

    // CORS headers
    const corsHeaders = {
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
      "Access-Control-Allow-Headers": "Content-Type, Authorization",
    };

    // Handle CORS preflight
    if (request.method === "OPTIONS") {
      return new Response(null, { headers: corsHeaders });
    }

    try {
      // Route handlers
      switch (url.pathname) {
        case "/":
          return handleRoot(env);

        case "/health":
          return handleHealth(env);

        case "/api/upload/presigned":
          return handlePresignedUpload(request, env);

        case "/api/webhook":
          return handleWebhook(request, env);

        default:
          return new Response("Not Found", {
            status: 404,
            headers: corsHeaders,
          });
      }
    } catch (error) {
      console.error("Worker error:", error);
      return new Response("Internal Server Error", {
        status: 500,
        headers: corsHeaders,
      });
    }
  },
};

async function handleRoot(env: Env): Promise<Response> {
  return new Response(
    JSON.stringify({
      message: "🚀 API Worker is running!",
      environment: env.NODE_ENV,
      timestamp: new Date().toISOString(),
    }),
    {
      headers: { "Content-Type": "application/json" },
    }
  );
}

async function handleHealth(env: Env): Promise<Response> {
  return new Response(
    JSON.stringify({
      status: "healthy",
      environment: env.NODE_ENV,
      services: {
        r2: "connected",
        kv: "connected",
      },
    }),
    {
      headers: { "Content-Type": "application/json" },
    }
  );
}

async function handlePresignedUpload(request: Request, _env: Env): Promise<Response> {
  if (request.method !== "POST") {
    return new Response("Method not allowed", { status: 405 });
  }

  try {
    const { filename } = (await request.json()) as {
      filename: string;
      contentType: string;
    };

    // Generate unique filename
    const uniqueFilename = `${Date.now()}-${filename}`;

    // For now, return a simple upload URL
    // In production, you would create a proper presigned URL
    return new Response(
      JSON.stringify({
        uploadUrl: `/api/upload/${uniqueFilename}`,
        filename: uniqueFilename,
      }),
      {
        headers: { "Content-Type": "application/json" },
      }
    );
  } catch (error) {
    console.error("Presigned upload error:", error);
    return new Response("Invalid request", { status: 400 });
  }
}

async function handleWebhook(request: Request, env: Env): Promise<Response> {
  if (request.method !== "POST") {
    return new Response("Method not allowed", { status: 405 });
  }

  try {
    const payload = await request.json();

    // Log webhook payload
    console.log("Webhook received:", payload);

    // Store in KV for processing
    await env.CACHE.put(
      `webhook:${Date.now()}`,
      JSON.stringify(payload),
      { expirationTtl: 86400 } // 24 hours
    );

    return new Response(
      JSON.stringify({
        status: "received",
        timestamp: new Date().toISOString(),
      }),
      {
        headers: { "Content-Type": "application/json" },
      }
    );
  } catch (error) {
    console.error("Webhook error:", error);
    return new Response("Invalid webhook payload", { status: 400 });
  }
}
