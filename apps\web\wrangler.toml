# Cloudflare Worker Configuration for Unified App
name = "remix-cloudflare-neon-starter"
main = "./worker.js"
compatibility_date = "2025-06-26"
compatibility_flags = ["nodejs_compat"]

# Assets configuration for static files
[assets]
directory = "./build/client"
binding = "ASSETS"

# Module worker format
[build]
command = ""

# Environment variables for Pages Functions
[vars]
NODE_ENV = "development"

# Cloudflare AI Binding (shared with worker)
[ai]
binding = "AI"

# R2 Storage Binding (shared with worker)
[[r2_buckets]]
binding = "R2_BUCKET"
bucket_name = "remix-cf-neon-starter-r2"
preview_bucket_name = "remix-cf-neon-starter-r2-preview"

# KV namespace bindings for caching and sessions (configure with real IDs)
# [[kv_namespaces]]
# binding = "CACHE"
# id = "your-kv-namespace-id"
# preview_id = "your-preview-kv-namespace-id"

# D1 database binding (if needed)
# [[d1_databases]]
# binding = "DB"
# database_name = "app-database"
# database_id = "your-database-id"

# Environment-specific overrides (Pages only supports "preview" and "production")
[env.production]
vars = { NODE_ENV = "production" }

# Production environment bindings
[env.production.ai]
binding = "AI"

[[env.production.r2_buckets]]
binding = "R2_BUCKET"
bucket_name = "remix-cf-neon-starter-r2"

[env.preview]
vars = { NODE_ENV = "development" }

# Preview environment bindings
[env.preview.ai]
binding = "AI"

[[env.preview.r2_buckets]]
binding = "R2_BUCKET"
bucket_name = "remix-cf-neon-starter-r2-preview"

# Custom domains (configure in Cloudflare Dashboard)
# routes = [
#   { pattern = "example.com/*", zone_name = "example.com" }
# ]
