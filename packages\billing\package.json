{"name": "@repo/billing", "version": "0.1.0", "private": true, "type": "module", "main": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": "./dist/index.js", "./providers/*": "./dist/providers/*.js", "./types": "./dist/types.js"}, "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "type-check": "tsc --noEmit"}, "dependencies": {"@repo/shared": "workspace:*", "@repo/db": "workspace:*", "stripe": "^14.0.0", "lemonsqueezy.ts": "^0.1.8", "zod": "^3.22.4"}, "devDependencies": {"@repo/typescript-config": "workspace:*", "typescript": "^5.8.2"}}