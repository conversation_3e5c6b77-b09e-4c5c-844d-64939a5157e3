# 🚀 Deployment Guide

本项目支持一键部署到 Cloudflare 平台。

## 📋 部署前准备

### 1. 安装 Wrangler CLI
```bash
npm install -g wrangler
# 或者使用项目中的版本
pnpm exec wrangler --version
```

### 2. 登录 Cloudflare
```bash
wrangler auth login
```

### 3. 配置环境变量
确保在 Cloudflare 控制台中设置了必要的环境变量和秘钥。

## 🛠️ 部署命令

### 一键部署所有应用
```bash
pnpm run deploy:all
```
这个命令会：
1. 构建所有应用
2. 部署 Worker API 服务
3. 部署 Web 应用

### 部署统一应用

#### 部署到 Cloudflare Workers
```bash
pnpm run deploy
```

#### 或者直接在web目录中部署
```bash
cd apps/web
pnpm run deploy
```

## 📦 部署架构

```
┌─────────────────────────────────────────┐
│         Cloudflare Worker               │
│                                         │
│   apps/web (Unified Application)        │
│   ├── Static Assets (build/client)      │
│   ├── Server-Side Rendering             │
│   ├── API Routes (/api/*)               │
│   └── Background Services               │
│                                         │
│   remix-cloudflare-neon-starter         │
└─────────────────────────────────────────┘
```

## 🔧 配置文件

### Web 应用 (apps/web/wrangler.toml)
- **部署类型**: Cloudflare Pages
- **构建输出**: `./build/client` (通过 `pages_build_output_dir` 配置)
- **项目名称**: `remix-cloudflare-neon-starter-web`
- **域名**: 通过 Pages 自动分配
- **绑定**: AI、R2存储等共享资源

### Worker API (apps/worker/wrangler.toml)
- **部署类型**: Cloudflare Workers
- **入口文件**: `./dist/index.js`
- **域名**: 通过 Workers 自动分配
- **绑定**: AI、R2存储、数据库等

## 🌐 访问地址

部署完成后，你会获得两个地址：

1. **Web 应用**: `https://remix-cloudflare-neon-starter.pages.dev`
2. **API 服务**: `https://api-worker.your-subdomain.workers.dev`

## 📝 部署日志

### 成功示例
```bash
$ pnpm run deploy:all

> sss@ deploy:all /path/to/project
> pnpm run build && pnpm run deploy:worker && pnpm run deploy:web

✓ Built successfully
✓ Worker deployed to https://api-worker.your-subdomain.workers.dev
✓ Pages deployed to https://remix-cloudflare-neon-starter.pages.dev
```

## 🔍 故障排除

### 常见问题

1. **认证失败**
   ```bash
   wrangler auth login
   ```

2. **构建失败**
   ```bash
   pnpm run build
   ```

3. **环境变量缺失**
   - 检查 wrangler.toml 配置
   - 在 Cloudflare 控制台中设置秘钥

4. **域名配置**
   - Pages: 在 Cloudflare 控制台配置自定义域名
   - Workers: 在 wrangler.toml 中配置路由

## 🚨 生产环境注意事项

1. **环境变量**: 确保生产环境的秘钥已正确设置
2. **域名配置**: 配置自定义域名和 SSL 证书
3. **缓存策略**: 检查 CDN 缓存配置
4. **监控**: 设置 Cloudflare Analytics 和错误监控
