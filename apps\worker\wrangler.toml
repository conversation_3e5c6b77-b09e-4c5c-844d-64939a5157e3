name = "remix-cloudflare-neon-starter"
main = "./dist/index.js"
compatibility_date = "2025-06-26"
compatibility_flags = ["nodejs_compat"]

[vars]
NODE_ENV = "development"

# R2 Storage Binding
[[r2_buckets]]
binding = "R2_BUCKET"
bucket_name = "remix-cf-neon-starter-r2"
preview_bucket_name = "remix-cf-neon-starter-r2-preview"

# Cloudflare AI Binding
[ai]
binding = "AI"

# D1 database binding (if needed)
# [[d1_databases]]
# binding = "DB"
# database_name = "app-database"
# database_id = "your-database-id"

# Environment-specific overrides
[env.production]
vars = { NODE_ENV = "production" }

[env.development]
vars = { NODE_ENV = "development" }