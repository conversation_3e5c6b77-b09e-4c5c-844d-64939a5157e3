{"name": "sss", "private": true, "scripts": {"build": "turbo run build", "dev": "turbo run dev", "lint": "biome check .", "lint:fix": "biome check --write .", "format": "biome format --write .", "check-types": "turbo run check-types", "deploy": "turbo run deploy --filter=web", "deploy:web": "turbo run deploy --filter=web"}, "devDependencies": {"@biomejs/biome": "^2.0.6", "turbo": "^2.5.4", "typescript": "5.8.3", "wrangler": "^4.22.0"}, "packageManager": "pnpm@9.0.0", "engines": {"node": ">=18"}, "pnpm": {"overrides": {"esbuild": "^0.21.5"}}}