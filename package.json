{"name": "sss", "private": true, "scripts": {"build": "turbo run build", "dev": "turbo run dev", "lint": "biome check .", "lint:fix": "biome check --write .", "format": "biome format --write .", "check-types": "turbo run check-types", "deploy": "turbo run deploy", "deploy:web": "turbo run deploy --filter=web", "deploy:worker": "turbo run deploy --filter=worker", "deploy:all": "pnpm run build && pnpm run deploy:worker && pnpm run deploy:web"}, "devDependencies": {"@biomejs/biome": "^1.9.4", "turbo": "^2.5.4", "typescript": "5.8.2", "wrangler": "^3.114.10"}, "packageManager": "pnpm@9.0.0", "engines": {"node": ">=18"}, "pnpm": {"overrides": {"esbuild": "^0.21.5"}}}