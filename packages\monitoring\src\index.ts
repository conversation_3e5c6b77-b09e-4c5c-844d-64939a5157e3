export * from "./logger.js";
export * from "./metrics.js";
export * from "./errors.js";

// Import classes for local use
import { Logger } from "./logger.js";
import { MetricsCollector, BusinessMetrics, PerformanceMonitor } from "./metrics.js";
import { ErrorTracker } from "./errors.js";

// Re-export commonly used classes and functions
export {
  Logger,
  createLogger,
  createLoggingMiddleware,
} from "./logger.js";

export {
  MetricsCollector,
  BusinessMetrics,
  PerformanceMonitor,
  getMetricsCollector,
  getBusinessMetrics,
  getPerformanceMonitor,
  createMetricsMiddleware,
} from "./metrics.js";

export {
  ErrorTracker,
  ValidationError,
  AuthenticationError,
  AuthorizationError,
  RateLimitError,
  ExternalServiceError,
  DatabaseError,
  getErrorTracker,
  createErrorHandlingMiddleware,
  withErrorTracking,
} from "./errors.js";

// Convenience function to initialize all monitoring
export function initializeMonitoring(config: {
  service: string;
  environment: string;
  version?: string;
  logLevel?: "debug" | "info" | "warn" | "error" | "fatal";
}) {
  const logger = Logger.getInstance({
    level: config.logLevel || "info",
    service: config.service,
    environment: config.environment,
    version: config.version,
  });

  const errorTracker = new ErrorTracker({
    environment: config.environment,
    version: config.version,
  });

  return {
    logger,
    errorTracker,
    metricsCollector: new MetricsCollector(),
    businessMetrics: new BusinessMetrics(new MetricsCollector()),
    performanceMonitor: new PerformanceMonitor(new MetricsCollector()),
  };
}